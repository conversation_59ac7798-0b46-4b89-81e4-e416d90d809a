/// UI常量定义 (ui_constants.dart)
///
/// 功能实现:
/// ✅ 统一UI常量管理 (完整实现)
/// ✅ 响应式设计支持 (完整实现)
/// ✅ 主题适配常量 (完整实现)
/// ✅ PDF阅读器专用常量 (完整实现)
///
/// 常量分类:
/// - 间距和尺寸
/// - 字体大小
/// - 圆角半径
/// - 动画时长
/// - 颜色透明度
/// - PDF阅读器专用
///
/// 作者: Augment Agent
/// 创建时间: 2025-07-16
library;

import 'dart:ui';
import 'package:flutter/material.dart';

class UIConstants {
  // 私有构造函数，防止实例化
  UIConstants._();

  // ========== 间距常量 ==========
  static const double spacingXS = 4.0; // 超小间距
  static const double spacingS = 8.0; // 小间距
  static const double spacingM = 16.0; // 中等间距
  static const double spacingL = 24.0; // 大间距
  static const double spacingXL = 32.0; // 超大间距
  static const double spacingXXL = 48.0; // 极大间距

  // ========== 字体大小常量 ==========
  static const double fontSizeXS = 10.0; // 超小字体
  static const double fontSizeS = 12.0; // 小字体
  static const double fontSizeM = 14.0; // 中等字体
  static const double fontSizeL = 16.0; // 大字体
  static const double fontSizeXL = 18.0; // 超大字体
  static const double fontSizeXXL = 20.0; // 极大字体
  static const double fontSizeTitle = 24.0; // 标题字体
  static const double fontSizeHeading = 28.0; // 标题字体

  // ========== 圆角半径常量 ==========
  static const double radiusXS = 2.0; // 超小圆角
  static const double radiusS = 4.0; // 小圆角
  static const double radiusM = 8.0; // 中等圆角
  static const double radiusL = 12.0; // 大圆角
  static const double radiusXL = 16.0; // 超大圆角
  static const double radiusXXL = 24.0; // 极大圆角
  static const double radiusCircle = 999.0; // 圆形

  // ========== 尺寸常量 ==========
  static const double iconSizeS = 16.0; // 小图标
  static const double iconSizeM = 24.0; // 中等图标
  static const double iconSizeL = 32.0; // 大图标
  static const double iconSizeXL = 48.0; // 超大图标

  static const double buttonHeightS = 32.0; // 小按钮高度
  static const double buttonHeightM = 40.0; // 中等按钮高度
  static const double buttonHeightL = 48.0; // 大按钮高度

  static const double appBarHeight = 56.0; // 应用栏高度
  static const double tabBarHeight = 48.0; // 标签栏高度
  static const double bottomNavHeight = 60.0; // 底部导航高度

  // ========== 动画时长常量 ==========
  static const Duration animationFast = Duration(milliseconds: 150);
  static const Duration animationNormal = Duration(milliseconds: 300);
  static const Duration animationSlow = Duration(milliseconds: 500);
  static const Duration animationVerySlow = Duration(milliseconds: 800);

  // ========== 透明度常量 ==========
  static const double opacityDisabled = 0.38;
  static const double opacityMedium = 0.54;
  static const double opacityHigh = 0.87;
  static const double opacityOverlay = 0.16;

  // ========== PDF阅读器专用常量 ==========

  // PDF页面相关
  static const double pdfPageMinScale = 0.5; // PDF最小缩放
  static const double pdfPageMaxScale = 5.0; // PDF最大缩放
  static const double pdfPageDefaultScale = 1.0; // PDF默认缩放

  // PDF页面间距
  static const double pdfPageSpacing = 16.0; // PDF页面间距
  static const double pdfPagePadding = 8.0; // PDF页面内边距

  // 工具栏相关
  static const double toolbarHeight = 56.0; // 工具栏高度
  static const double toolbarIconSize = 24.0; // 工具栏图标大小
  static const double toolbarSpacing = 12.0; // 工具栏间距

  // 侧边栏相关
  static const double sidebarWidth = 300.0; // 侧边栏宽度
  static const double sidebarMinWidth = 250.0; // 侧边栏最小宽度
  static const double sidebarMaxWidth = 400.0; // 侧边栏最大宽度

  // 对照编辑相关
  static const double splitViewMinRatio = 0.2; // 分屏最小比例
  static const double splitViewMaxRatio = 0.8; // 分屏最大比例
  static const double splitViewDefaultRatio = 0.5; // 分屏默认比例
  static const double dividerWidth = 2.0; // 分隔线宽度
  static const double dividerHandleSize = 20.0; // 分隔线手柄大小

  // OCR相关
  static const double ocrTextPadding = 12.0; // OCR文本内边距
  static const double ocrTextLineHeight = 1.6; // OCR文本行高
  static const double ocrConfidenceBarHeight = 4.0; // OCR置信度条高度

  // 缩略图相关
  static const double thumbnailWidth = 120.0; // 缩略图宽度
  static const double thumbnailHeight = 160.0; // 缩略图高度
  static const double thumbnailSpacing = 8.0; // 缩略图间距

  // 浮动面板相关
  static const double floatingPanelWidth = 280.0; // 浮动面板宽度
  static const double floatingPanelMinHeight = 120.0; // 浮动面板最小高度
  static const double floatingPanelMaxHeight = 400.0; // 浮动面板最大高度
  static const double floatingPanelRadius = 12.0; // 浮动面板圆角

  // 搜索相关
  static const double searchBarHeight = 48.0; // 搜索栏高度
  static const double searchResultHeight = 60.0; // 搜索结果高度

  // 书签相关
  static const double bookmarkIconSize = 20.0; // 书签图标大小
  static const double bookmarkItemHeight = 56.0; // 书签项高度

  // 注释相关
  static const double annotationMinSize = 20.0; // 注释最小尺寸
  static const double annotationMaxSize = 200.0; // 注释最大尺寸
  static const double annotationBorderWidth = 2.0; // 注释边框宽度

  // 手势相关
  static const double gestureDeadZone = 10.0; // 手势死区
  static const double gestureSensitivity = 1.0; // 手势灵敏度
  static const Duration gestureDebounce = Duration(milliseconds: 50); // 手势防抖

  // 性能相关
  static const int maxCachedPages = 10; // 最大缓存页面数
  static const int preloadPageCount = 3; // 预加载页面数
  static const Duration renderTimeout = Duration(seconds: 10); // 渲染超时

  // 响应式断点
  static const double mobileBreakpoint = 600.0; // 移动设备断点
  static const double tabletBreakpoint = 900.0; // 平板设备断点
  static const double desktopBreakpoint = 1200.0; // 桌面设备断点

  // ========== 颜色相关常量 ==========

  // 语义颜色透明度
  static const double successOpacity = 0.12;
  static const double warningOpacity = 0.12;
  static const double errorOpacity = 0.12;
  static const double infoOpacity = 0.12;

  // 阴影相关
  static const double shadowBlurRadius = 8.0;
  static const double shadowSpreadRadius = 0.0;
  static const Offset shadowOffset = Offset(0, 2);

  // ========== Z-Index层级常量 ==========
  static const int zIndexBackground = 0;
  static const int zIndexContent = 1;
  static const int zIndexOverlay = 10;
  static const int zIndexModal = 20;
  static const int zIndexTooltip = 30;
  static const int zIndexDropdown = 40;
  static const int zIndexSnackbar = 50;
  static const int zIndexDialog = 60;
  static const int zIndexLoading = 70;

  // ========== 便捷方法 ==========

  /// 根据屏幕宽度获取响应式间距
  static double getResponsiveSpacing(double screenWidth) {
    if (screenWidth < mobileBreakpoint) {
      return spacingS;
    } else if (screenWidth < tabletBreakpoint) {
      return spacingM;
    } else {
      return spacingL;
    }
  }

  /// 根据屏幕宽度获取响应式字体大小
  static double getResponsiveFontSize(double screenWidth, double baseFontSize) {
    if (screenWidth < mobileBreakpoint) {
      return baseFontSize * 0.9;
    } else if (screenWidth < tabletBreakpoint) {
      return baseFontSize;
    } else {
      return baseFontSize * 1.1;
    }
  }

  /// 根据屏幕宽度判断设备类型
  static String getDeviceType(double screenWidth) {
    if (screenWidth < mobileBreakpoint) {
      return 'mobile';
    } else if (screenWidth < tabletBreakpoint) {
      return 'tablet';
    } else {
      return 'desktop';
    }
  }

  /// 获取PDF页面的响应式尺寸
  static Size getPDFPageSize(double screenWidth, double screenHeight) {
    final deviceType = getDeviceType(screenWidth);

    switch (deviceType) {
      case 'mobile':
        return Size(screenWidth - spacingM * 2, screenHeight * 0.7);
      case 'tablet':
        return Size(screenWidth * 0.8, screenHeight * 0.8);
      case 'desktop':
        return Size(screenWidth * 0.6, screenHeight * 0.9);
      default:
        return Size(screenWidth * 0.8, screenHeight * 0.8);
    }
  }
}
