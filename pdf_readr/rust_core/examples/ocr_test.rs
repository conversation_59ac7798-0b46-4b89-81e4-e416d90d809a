/// OCR模块测试示例
/// 
/// 测试Tesseract和PaddleOCR的集成功能
/// 
/// 运行方式: cargo run --example ocr_test --features ocr

use pdf_reader_core::ocr::{ImageData, initialize, health_check};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志
    env_logger::init();
    
    println!("🚀 开始OCR模块测试");
    
    // 1. 初始化OCR模块
    println!("📋 初始化OCR模块...");
    match initialize().await {
        Ok(_) => println!("✅ OCR模块初始化成功"),
        Err(e) => {
            println!("❌ OCR模块初始化失败: {}", e);
            return Ok(());
        }
    }
    
    // 2. 健康检查
    println!("🔍 执行健康检查...");
    match health_check().await {
        Ok(true) => println!("✅ OCR引擎健康检查通过"),
        Ok(false) => println!("⚠️ OCR引擎健康检查失败"),
        Err(e) => println!("❌ 健康检查错误: {}", e),
    }
    
    // 3. 创建测试图像数据
    println!("🖼️ 创建测试图像...");
    let test_image = create_test_image();
    
    // 4. 测试OCR识别（如果有可用的服务）
    #[cfg(feature = "ocr")]
    {
        match pdf_reader_core::ocr::create_simple_ocr_service().await {
            Ok(ocr_service) => {
                println!("✅ OCR服务创建成功");
                
                // 获取支持的语言
                let languages = ocr_service.get_supported_languages();
                println!("📝 支持的语言: {:?}", languages);
                
                // 尝试识别测试图像
                println!("🔍 开始OCR识别...");
                match ocr_service.recognize_image(test_image).await {
                    Ok(result) => {
                        println!("✅ OCR识别成功!");
                        println!("   引擎: {:?}", result.engine);
                        println!("   置信度: {:.2}", result.overall_confidence);
                        println!("   识别文本: {}", result.get_full_text());
                        println!("   处理时间: {}ms", result.processing_time_ms);
                    }
                    Err(e) => {
                        println!("❌ OCR识别失败: {}", e);
                    }
                }
            }
            Err(e) => {
                println!("❌ OCR服务创建失败: {}", e);
            }
        }
    }
    
    println!("🎉 OCR模块测试完成");
    Ok(())
}

/// 创建一个简单的测试图像
fn create_test_image() -> ImageData {
    // 创建一个简单的1x1像素PNG图像用于测试
    let png_data = vec![
        0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, // PNG signature
        0x00, 0x00, 0x00, 0x0D, // IHDR chunk length
        0x49, 0x48, 0x44, 0x52, // IHDR
        0x00, 0x00, 0x00, 0x01, // width: 1
        0x00, 0x00, 0x00, 0x01, // height: 1
        0x08, 0x02, 0x00, 0x00, 0x00, // bit depth, color type, compression, filter, interlace
        0x90, 0x77, 0x53, 0xDE, // CRC
        0x00, 0x00, 0x00, 0x0C, // IDAT chunk length
        0x49, 0x44, 0x41, 0x54, // IDAT
        0x08, 0x99, 0x01, 0x01, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x02, 0x00, 0x01, // data
        0xE2, 0x21, 0xBC, 0x33, // CRC
        0x00, 0x00, 0x00, 0x00, // IEND chunk length
        0x49, 0x45, 0x4E, 0x44, // IEND
        0xAE, 0x42, 0x60, 0x82, // CRC
    ];
    
    ImageData::new(png_data, 1, 1, "png".to_string())
}
