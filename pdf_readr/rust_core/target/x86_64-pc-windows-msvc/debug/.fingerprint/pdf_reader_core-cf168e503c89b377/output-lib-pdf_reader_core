{"$message_type":"diagnostic","message":"the name `KeyIni<PERSON>` is defined multiple times","code":{"code":"E0252","explanation":"Two items of the same name cannot be imported without rebinding one of the\nitems under a new local name.\n\nErroneous code example:\n\n```compile_fail,E0252\nuse foo::baz;\nuse bar::baz; // error, do `use bar::baz as quux` instead\n\nfn main() {}\n\nmod foo {\n    pub struct baz;\n}\n\nmod bar {\n    pub mod baz {}\n}\n```\n\nYou can use aliases in order to fix this error. Example:\n\n```\nuse foo::baz as foo_baz;\nuse bar::baz; // ok!\n\nfn main() {}\n\nmod foo {\n    pub struct baz;\n}\n\nmod bar {\n    pub mod baz {}\n}\n```\n\nOr you can reference the item with its parent:\n\n```\nuse bar::baz;\n\nfn main() {\n    let x = foo::baz; // ok!\n}\n\nmod foo {\n    pub struct baz;\n}\n\nmod bar {\n    pub mod baz {}\n}\n```\n"},"level":"error","spans":[{"file_name":"src\\utils\\crypto_utils\\encryptor.rs","byte_start":660,"byte_end":667,"line_start":18,"line_end":18,"column_start":42,"column_end":49,"is_primary":true,"text":[{"text":"use chacha20poly1305::{ChaCha20Poly1305, KeyInit};","highlight_start":42,"highlight_end":49}],"label":"`KeyInit` reimported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\utils\\crypto_utils\\encryptor.rs","byte_start":584,"byte_end":591,"line_start":16,"line_end":16,"column_start":49,"column_end":56,"is_primary":false,"text":[{"text":"use aes_gcm::{Aes256Gcm, Aes128Gcm, Key, Nonce, KeyInit};","highlight_start":49,"highlight_end":56}],"label":"previous import of the trait `KeyInit` here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`KeyInit` must be defined only once in the type namespace of this module","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove unnecessary import","code":null,"level":"help","spans":[{"file_name":"src\\utils\\crypto_utils\\encryptor.rs","byte_start":658,"byte_end":667,"line_start":18,"line_end":18,"column_start":40,"column_end":49,"is_primary":true,"text":[{"text":"use chacha20poly1305::{ChaCha20Poly1305, KeyInit};","highlight_start":40,"highlight_end":49}],"label":null,"suggested_replacement":"","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0252]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: the name `KeyInit` is defined multiple times\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\utils\\crypto_utils\\encryptor.rs:18:42\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m16\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse aes_gcm::{Aes256Gcm, Aes128Gcm, Key, Nonce, KeyInit};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mprevious import of the trait `KeyInit` here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m17\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse aes_gcm::aead::Aead;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m18\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse chacha20poly1305::{ChaCha20Poly1305, KeyInit};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m`KeyInit` reimported here\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `KeyInit` must be defined only once in the type namespace of this module\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `Blake2s256`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\utils\\crypto_utils\\hasher.rs","byte_start":604,"byte_end":614,"line_start":18,"line_end":18,"column_start":26,"column_end":36,"is_primary":true,"text":[{"text":"use blake2::{Blake2b512, Blake2s256};","highlight_start":26,"highlight_end":36}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\utils\\crypto_utils\\hasher.rs","byte_start":602,"byte_end":614,"line_start":18,"line_end":18,"column_start":24,"column_end":36,"is_primary":true,"text":[{"text":"use blake2::{Blake2b512, Blake2s256};","highlight_start":24,"highlight_end":36}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\utils\\crypto_utils\\hasher.rs","byte_start":591,"byte_end":592,"line_start":18,"line_end":18,"column_start":13,"column_end":14,"is_primary":true,"text":[{"text":"use blake2::{Blake2b512, Blake2s256};","highlight_start":13,"highlight_end":14}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\utils\\crypto_utils\\hasher.rs","byte_start":614,"byte_end":615,"line_start":18,"line_end":18,"column_start":36,"column_end":37,"is_primary":true,"text":[{"text":"use blake2::{Blake2b512, Blake2s256};","highlight_start":36,"highlight_end":37}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `Blake2s256`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\utils\\crypto_utils\\hasher.rs:18:26\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m18\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse blake2::{Blake2b512, Blake2s256};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `ChaCha20Poly1305` and `KeyInit`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\utils\\crypto_utils\\encryptor.rs","byte_start":642,"byte_end":658,"line_start":18,"line_end":18,"column_start":24,"column_end":40,"is_primary":true,"text":[{"text":"use chacha20poly1305::{ChaCha20Poly1305, KeyInit};","highlight_start":24,"highlight_end":40}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\utils\\crypto_utils\\encryptor.rs","byte_start":660,"byte_end":667,"line_start":18,"line_end":18,"column_start":42,"column_end":49,"is_primary":true,"text":[{"text":"use chacha20poly1305::{ChaCha20Poly1305, KeyInit};","highlight_start":42,"highlight_end":49}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\utils\\crypto_utils\\encryptor.rs","byte_start":619,"byte_end":670,"line_start":18,"line_end":19,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use chacha20poly1305::{ChaCha20Poly1305, KeyInit};","highlight_start":1,"highlight_end":51},{"text":"use rand_core::{OsRng, RngCore};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `ChaCha20Poly1305` and `KeyInit`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\utils\\crypto_utils\\encryptor.rs:18:24\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m18\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse chacha20poly1305::{ChaCha20Poly1305, KeyInit};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `real_chacha20_poly1305_encrypt` found for reference `&CryptoEncryptor` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\utils\\crypto_utils\\encryptor.rs","byte_start":2072,"byte_end":2102,"line_start":58,"line_end":58,"column_start":51,"column_end":81,"is_primary":true,"text":[{"text":"            EncryptionAlgorithm::ChaCha20 => self.real_chacha20_poly1305_encrypt(data, key)?,","highlight_start":51,"highlight_end":81}],"label":"method not found in `&CryptoEncryptor`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no method named `real_chacha20_poly1305_encrypt` found for reference `&CryptoEncryptor` in the current scope\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\utils\\crypto_utils\\encryptor.rs:58:51\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m58\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            EncryptionAlgorithm::ChaCha20 => self.real_chacha20_poly1305_encrypt(data, key)?,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod not found in `&CryptoEncryptor`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no field `nonce` on type `EncryptionResult`","code":{"code":"E0609","explanation":"Attempted to access a nonexistent field in a struct.\n\nErroneous code example:\n\n```compile_fail,E0609\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.foo); // error: no field `foo` on type `StructWithFields`\n```\n\nTo fix this error, check that you didn't misspell the field's name or that the\nfield actually exists. Example:\n\n```\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.x); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\utils\\crypto_utils\\encryptor.rs","byte_start":2547,"byte_end":2552,"line_start":69,"line_end":69,"column_start":16,"column_end":21,"is_primary":true,"text":[{"text":"        result.nonce = Some(nonce);","highlight_start":16,"highlight_end":21}],"label":"unknown field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"available fields are: `algorithm`, `encrypted_data`, `iv`, `tag`, `input_size`, `processing_time_ms`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0609]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no field `nonce` on type `EncryptionResult`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\utils\\crypto_utils\\encryptor.rs:69:16\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m69\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        result.nonce = Some(nonce);\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9munknown field\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: available fields are: `algorithm`, `encrypted_data`, `iv`, `tag`, `input_size`, `processing_time_ms`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no field `auth_tag` on type `EncryptionResult`","code":{"code":"E0609","explanation":"Attempted to access a nonexistent field in a struct.\n\nErroneous code example:\n\n```compile_fail,E0609\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.foo); // error: no field `foo` on type `StructWithFields`\n```\n\nTo fix this error, check that you didn't misspell the field's name or that the\nfield actually exists. Example:\n\n```\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.x); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\utils\\crypto_utils\\encryptor.rs","byte_start":2583,"byte_end":2591,"line_start":70,"line_end":70,"column_start":16,"column_end":24,"is_primary":true,"text":[{"text":"        result.auth_tag = Some(auth_tag);","highlight_start":16,"highlight_end":24}],"label":"unknown field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"available fields are: `algorithm`, `encrypted_data`, `iv`, `tag`, `input_size`, `processing_time_ms`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0609]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no field `auth_tag` on type `EncryptionResult`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\utils\\crypto_utils\\encryptor.rs:70:16\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m70\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        result.auth_tag = Some(auth_tag);\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9munknown field\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: available fields are: `algorithm`, `encrypted_data`, `iv`, `tag`, `input_size`, `processing_time_ms`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `real_aes128_gcm_decrypt` found for reference `&CryptoEncryptor` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\utils\\crypto_utils\\encryptor.rs","byte_start":3460,"byte_end":3483,"line_start":94,"line_end":94,"column_start":49,"column_end":72,"is_primary":true,"text":[{"text":"            EncryptionAlgorithm::Aes128 => self.real_aes128_gcm_decrypt(encrypted_data, key, nonce, auth_tag),","highlight_start":49,"highlight_end":72}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"there is a method `real_aes128_gcm_encrypt` with a similar name, but with different arguments","code":null,"level":"help","spans":[{"file_name":"src\\utils\\crypto_utils\\encryptor.rs","byte_start":4000,"byte_end":4100,"line_start":105,"line_end":105,"column_start":5,"column_end":105,"is_primary":true,"text":[{"text":"    fn real_aes128_gcm_encrypt(&self, data: &[u8], key: &[u8]) -> AppResult<(Vec<u8>, Vec<u8>, Vec<u8>)> {","highlight_start":5,"highlight_end":105}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no method named `real_aes128_gcm_decrypt` found for reference `&CryptoEncryptor` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\utils\\crypto_utils\\encryptor.rs:94:49\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m94\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            EncryptionAlgorithm::Aes128 => self.real_aes128_gcm_decrypt(encrypted_data, key, nonce, auth_tag),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is a method `real_aes128_gcm_encrypt` with a similar name, but with different arguments\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\utils\\crypto_utils\\encryptor.rs:105:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m105\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn real_aes128_gcm_encrypt(&self, data: &[u8], key: &[u8]) -> AppResult<(Vec<u8>, Vec<u8>, Vec<u8>)> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `real_aes256_gcm_decrypt` found for reference `&CryptoEncryptor` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\utils\\crypto_utils\\encryptor.rs","byte_start":3571,"byte_end":3594,"line_start":95,"line_end":95,"column_start":49,"column_end":72,"is_primary":true,"text":[{"text":"            EncryptionAlgorithm::Aes256 => self.real_aes256_gcm_decrypt(encrypted_data, key, nonce, auth_tag),","highlight_start":49,"highlight_end":72}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"there is a method `real_aes256_gcm_encrypt` with a similar name, but with different arguments","code":null,"level":"help","spans":[{"file_name":"src\\utils\\crypto_utils\\encryptor.rs","byte_start":4930,"byte_end":5030,"line_start":126,"line_end":126,"column_start":5,"column_end":105,"is_primary":true,"text":[{"text":"    fn real_aes256_gcm_encrypt(&self, data: &[u8], key: &[u8]) -> AppResult<(Vec<u8>, Vec<u8>, Vec<u8>)> {","highlight_start":5,"highlight_end":105}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no method named `real_aes256_gcm_decrypt` found for reference `&CryptoEncryptor` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\utils\\crypto_utils\\encryptor.rs:95:49\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m95\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            EncryptionAlgorithm::Aes256 => self.real_aes256_gcm_decrypt(encrypted_data, key, nonce, auth_tag),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is a method `real_aes256_gcm_encrypt` with a similar name, but with different arguments\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\utils\\crypto_utils\\encryptor.rs:126:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m126\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn real_aes256_gcm_encrypt(&self, data: &[u8], key: &[u8]) -> AppResult<(Vec<u8>, Vec<u8>, Vec<u8>)> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `real_chacha20_poly1305_decrypt` found for reference `&CryptoEncryptor` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\utils\\crypto_utils\\encryptor.rs","byte_start":3684,"byte_end":3714,"line_start":96,"line_end":96,"column_start":51,"column_end":81,"is_primary":true,"text":[{"text":"            EncryptionAlgorithm::ChaCha20 => self.real_chacha20_poly1305_decrypt(encrypted_data, key, nonce, auth_tag),","highlight_start":51,"highlight_end":81}],"label":"method not found in `&CryptoEncryptor`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no method named `real_chacha20_poly1305_decrypt` found for reference `&CryptoEncryptor` in the current scope\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\utils\\crypto_utils\\encryptor.rs:96:51\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m96\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            EncryptionAlgorithm::ChaCha20 => self.real_chacha20_poly1305_decrypt(encrypted_data, key, nonce, auth_tag),\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod not found in `&CryptoEncryptor`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"type annotations needed","code":{"code":"E0284","explanation":"This error occurs when the compiler is unable to unambiguously infer the\nreturn type of a function or method which is generic on return type, such\nas the `collect` method for `Iterator`s.\n\nFor example:\n\n```compile_fail,E0284\nfn main() {\n    let n: u32 = 1;\n    let mut d: u64 = 2;\n    d = d + n.into();\n}\n```\n\nHere we have an addition of `d` and `n.into()`. Hence, `n.into()` can return\nany type `T` where `u64: Add<T>`. On the other hand, the `into` method can\nreturn any type where `u32: Into<T>`.\n\nThe author of this code probably wants `into()` to return a `u64`, but the\ncompiler can't be sure that there isn't another type `T` where both\n`u32: Into<T>` and `u64: Add<T>`.\n\nTo resolve this error, use a concrete type for the intermediate expression:\n\n```\nfn main() {\n    let n: u32 = 1;\n    let mut d: u64 = 2;\n    let m: u64 = n.into();\n    d = d + m;\n}\n```\n"},"level":"error","spans":[{"file_name":"src\\utils\\crypto_utils\\encryptor.rs","byte_start":4139,"byte_end":4142,"line_start":106,"line_end":106,"column_start":37,"column_end":40,"is_primary":true,"text":[{"text":"        let cipher = Aes128Gcm::new(Key::from_slice(key));","highlight_start":37,"highlight_end":40}],"label":"cannot infer type for type parameter `B` declared on the type alias `Key`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"cannot satisfy `<_ as KeySizeUser>::KeySize == _`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0284]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: type annotations needed\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\utils\\crypto_utils\\encryptor.rs:106:37\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m106\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let cipher = Aes128Gcm::new(Key::from_slice(key));\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcannot infer type for type parameter `B` declared on the type alias `Key`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: cannot satisfy `<_ as KeySizeUser>::KeySize == _`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"type annotations needed","code":{"code":"E0283","explanation":"The compiler could not infer a type and asked for a type annotation.\n\nErroneous code example:\n\n```compile_fail,E0283\nlet x = \"hello\".chars().rev().collect();\n```\n\nThis error indicates that type inference did not result in one unique possible\ntype, and extra information is required. In most cases this can be provided\nby adding a type annotation. Sometimes you need to specify a generic type\nparameter manually.\n\nA common example is the `collect` method on `Iterator`. It has a generic type\nparameter with a `FromIterator` bound, which for a `char` iterator is\nimplemented by `Vec` and `String` among others. Consider the following snippet\nthat reverses the characters of a string:\n\nIn the first code example, the compiler cannot infer what the type of `x` should\nbe: `Vec<char>` and `String` are both suitable candidates. To specify which type\nto use, you can use a type annotation on `x`:\n\n```\nlet x: Vec<char> = \"hello\".chars().rev().collect();\n```\n\nIt is not necessary to annotate the full type. Once the ambiguity is resolved,\nthe compiler can infer the rest:\n\n```\nlet x: Vec<_> = \"hello\".chars().rev().collect();\n```\n\nAnother way to provide the compiler with enough information, is to specify the\ngeneric type parameter:\n\n```\nlet x = \"hello\".chars().rev().collect::<Vec<char>>();\n```\n\nAgain, you need not specify the full type if the compiler can infer it:\n\n```\nlet x = \"hello\".chars().rev().collect::<Vec<_>>();\n```\n\nWe can see a self-contained example below:\n\n```compile_fail,E0283\nstruct Foo;\n\nimpl Into<u32> for Foo {\n    fn into(self) -> u32 { 1 }\n}\n\nlet foo = Foo;\nlet bar: u32 = foo.into() * 1u32;\n```\n\nThis error can be solved by adding type annotations that provide the missing\ninformation to the compiler. In this case, the solution is to specify the\ntrait's type parameter:\n\n```\nstruct Foo;\n\nimpl Into<u32> for Foo {\n    fn into(self) -> u32 { 1 }\n}\n\nlet foo = Foo;\nlet bar: u32 = Into::<u32>::into(foo) * 1u32;\n```\n"},"level":"error","spans":[{"file_name":"src\\utils\\crypto_utils\\encryptor.rs","byte_start":4139,"byte_end":4142,"line_start":106,"line_end":106,"column_start":37,"column_end":40,"is_primary":true,"text":[{"text":"        let cipher = Aes128Gcm::new(Key::from_slice(key));","highlight_start":37,"highlight_end":40}],"label":"cannot infer type","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"multiple `impl`s satisfying `_: ArrayLength<u8>` found in the `generic_array` crate:\n- impl<T, N> ArrayLength<T> for UInt<N, B0>\n  where N: ArrayLength<T>;\n- impl<T, N> ArrayLength<T> for UInt<N, B1>\n  where N: ArrayLength<T>;\n- impl<T> ArrayLength<T> for UTerm;","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"required by a bound in `GenericArray`","code":null,"level":"note","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\generic-array-0.14.7\\src\\lib.rs","byte_start":4484,"byte_end":4498,"line_start":179,"line_end":179,"column_start":31,"column_end":45,"is_primary":true,"text":[{"text":"pub struct GenericArray<T, U: ArrayLength<T>> {","highlight_start":31,"highlight_end":45}],"label":"required by this bound in `GenericArray`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0283]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: type annotations needed\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\utils\\crypto_utils\\encryptor.rs:106:37\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m106\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let cipher = Aes128Gcm::new(Key::from_slice(key));\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcannot infer type\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: multiple `impl`s satisfying `_: ArrayLength<u8>` found in the `generic_array` crate:\u001b[0m\n\u001b[0m            - impl<T, N> ArrayLength<T> for UInt<N, B0>\u001b[0m\n\u001b[0m              where N: ArrayLength<T>;\u001b[0m\n\u001b[0m            - impl<T, N> ArrayLength<T> for UInt<N, B1>\u001b[0m\n\u001b[0m              where N: ArrayLength<T>;\u001b[0m\n\u001b[0m            - impl<T> ArrayLength<T> for UTerm;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required by a bound in `GenericArray`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\generic-array-0.14.7\\src\\lib.rs:179:31\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m179\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct GenericArray<T, U: ArrayLength<T>> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mrequired by this bound in `GenericArray`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 9 previous errors; 2 warnings emitted","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: aborting due to 9 previous errors; 2 warnings emitted\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"Some errors have detailed explanations: E0252, E0283, E0284, E0599, E0609.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;15mSome errors have detailed explanations: E0252, E0283, E0284, E0599, E0609.\u001b[0m\n"}
{"$message_type":"diagnostic","message":"For more information about an error, try `rustc --explain E0252`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;15mFor more information about an error, try `rustc --explain E0252`.\u001b[0m\n"}
