{"rustc": 1842507548689473721, "features": "[\"alloc\", \"default\", \"parking_lot\", \"std\"]", "declared_features": "[\"alloc\", \"default\", \"parking_lot\", \"std\"]", "target": 17561780016695937293, "profile": 17152269133238016429, "path": 10421471350071795876, "deps": [[4495526598637097934, "parking_lot", false, 2455109057135636223], [7620660491849607393, "futures_core", false, 3137036167924736813], [8081351675046095464, "lock_api", false, 7643365707691562168]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-pc-windows-msvc\\debug\\.fingerprint\\futures-intrusive-bd6ba4aa5c9f85bc\\dep-lib-futures_intrusive", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 9161124489350129226}