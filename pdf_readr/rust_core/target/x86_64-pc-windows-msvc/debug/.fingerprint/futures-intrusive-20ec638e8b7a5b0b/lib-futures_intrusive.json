{"rustc": 1842507548689473721, "features": "[\"alloc\", \"default\", \"parking_lot\", \"std\"]", "declared_features": "[\"alloc\", \"default\", \"parking_lot\", \"std\"]", "target": 17561780016695937293, "profile": 4596809407697463924, "path": 10421471350071795876, "deps": [[4495526598637097934, "parking_lot", false, 9858541975931175807], [7620660491849607393, "futures_core", false, 13983460499670342685], [8081351675046095464, "lock_api", false, 1171969721203796769]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-pc-windows-msvc\\debug\\.fingerprint\\futures-intrusive-20ec638e8b7a5b0b\\dep-lib-futures_intrusive", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 9161124489350129226}