@echo off
chcp 65001 >nul
echo 正在创建所有开发环境脚本...

REM 创建主安装脚本
echo 创建主安装脚本...
(
echo @echo off
echo chcp 65001 ^>nul
echo setlocal enabledelayedexpansion
echo.
echo echo ========================================
echo echo    PDF阅读器项目开发环境自动安装
echo echo ========================================
echo echo.
echo.
echo REM 检查管理员权限
echo net session ^>nul 2^>^&1
echo if %%errorLevel%% neq 0 ^(
echo     echo [错误] 请以管理员身份运行此脚本！
echo     echo 右键点击脚本 → "以管理员身份运行"
echo     pause
echo     exit /b 1
echo ^)
echo.
echo echo [信息] 开始安装开发环境...
echo echo.
echo.
echo REM 安装 Chocolatey
echo echo [1/6] 安装 Chocolatey 包管理器...
echo where choco ^>nul 2^>^&1
echo if %%errorLevel%% neq 0 ^(
echo     echo [安装] 正在安装 Chocolatey...
echo     powershell -Command "Set-ExecutionPolicy Bypass -Scope Process -Force; [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))"
echo     call refreshenv
echo ^) else ^(
echo     echo [跳过] Chocolatey 已安装
echo ^)
echo.
echo REM 安装 Rust
echo echo [2/6] 安装 Rust 开发环境...
echo where rustc ^>nul 2^>^&1
echo if %%errorLevel%% neq 0 ^(
echo     echo [安装] 正在安装 Rust...
echo     curl -sSf -o rustup-init.exe https://win.rustup.rs/x86_64
echo     rustup-init.exe -y --default-toolchain stable
echo     call refreshenv
echo ^) else ^(
echo     echo [跳过] Rust 已安装
echo ^)
echo.
echo REM 安装 Rust 组件
echo echo [配置] 安装 Rust 组件...
echo rustup component add rustfmt
echo rustup component add clippy
echo rustup component add rust-src
echo.
echo REM 安装 Node.js
echo echo [3/6] 安装 Node.js...
echo where node ^>nul 2^>^&1
echo if %%errorLevel%% neq 0 ^(
echo     choco install nodejs-lts -y
echo     call refreshenv
echo ^) else ^(
echo     echo [跳过] Node.js 已安装
echo ^)
echo.
echo REM 安装 Tesseract
echo echo [4/6] 安装 Tesseract OCR...
echo where tesseract ^>nul 2^>^&1
echo if %%errorLevel%% neq 0 ^(
echo     choco install tesseract -y
echo     call refreshenv
echo ^) else ^(
echo     echo [跳过] Tesseract 已安装
echo ^)
echo.
echo REM 安装开发工具
echo echo [5/6] 安装开发工具...
echo choco install git cmake llvm python3 -y
echo call refreshenv
echo.
echo REM 配置项目
echo echo [6/6] 配置项目环境...
echo if exist "rust_core\Cargo.toml" ^(
echo     cd rust_core
echo     cargo fetch
echo     cd ..
echo ^)
echo if exist "frontend\package.json" ^(
echo     cd frontend
echo     npm install
echo     cd ..
echo ^)
echo.
echo echo ========================================
echo echo    安装完成！
echo echo ========================================
echo echo 请重启终端以使环境变量生效
echo pause
) > setup_dev_environment.bat

REM 创建验证脚本
echo 创建验证脚本...
(
echo @echo off
echo chcp 65001 ^>nul
echo echo ========================================
echo echo    开发环境验证脚本
echo echo ========================================
echo echo.
echo.
echo echo [1] 检查 Rust 环境...
echo rustc --version 2^>nul ^|^| echo [错误] Rust 未安装
echo cargo --version 2^>nul ^|^| echo [错误] Cargo 未安装
echo.
echo echo [2] 检查 Node.js 环境...
echo node --version 2^>nul ^|^| echo [错误] Node.js 未安装
echo npm --version 2^>nul ^|^| echo [错误] npm 未安装
echo.
echo echo [3] 检查 OCR 环境...
echo tesseract --version 2^>nul ^|^| echo [错误] Tesseract 未安装
echo.
echo echo [4] 检查开发工具...
echo git --version 2^>nul ^|^| echo [错误] Git 未安装
echo.
echo echo [5] 测试项目编译...
echo if exist "rust_core\Cargo.toml" ^(
echo     cd rust_core
echo     echo 测试 Rust 项目编译...
echo     cargo check --features ocr
echo     cd ..
echo ^)
echo.
echo echo 验证完成！
echo pause
) > verify_installation.bat

REM 创建 IntelliJ IDEA 配置脚本
echo 创建 IntelliJ IDEA 配置脚本...
(
echo @echo off
echo chcp 65001 ^>nul
echo echo 配置 IntelliJ IDEA 项目...
echo.
echo if not exist ".idea" mkdir ".idea"
echo if not exist ".idea\runConfigurations" mkdir ".idea\runConfigurations"
echo.
echo echo 创建运行配置...
echo echo ^<?xml version="1.0" encoding="UTF-8"?^> ^> ".idea\modules.xml"
echo echo ^<project version="4"^> ^>^> ".idea\modules.xml"
echo echo   ^<component name="ProjectModuleManager"^> ^>^> ".idea\modules.xml"
echo echo     ^<modules^> ^>^> ".idea\modules.xml"
echo echo       ^<module fileurl="file://$PROJECT_DIR$/pdf_readr.iml" filepath="$PROJECT_DIR$/pdf_readr.iml" /^> ^>^> ".idea\modules.xml"
echo echo     ^</modules^> ^>^> ".idea\modules.xml"
echo echo   ^</component^> ^>^> ".idea\modules.xml"
echo echo ^</project^> ^>^> ".idea\modules.xml"
echo.
echo echo IntelliJ IDEA 配置完成！
echo echo 现在可以用 IntelliJ IDEA 打开项目了
echo pause
) > setup_intellij_idea.bat

echo.
echo ========================================
echo 所有脚本创建完成！
echo ========================================
echo.
echo 创建的脚本文件：
echo 1. setup_dev_environment.bat - 主安装脚本
echo 2. verify_installation.bat - 验证脚本  
echo 3. setup_intellij_idea.bat - IntelliJ IDEA 配置脚本
echo.
echo 使用方法：
echo 1. 右键 setup_dev_environment.bat → "以管理员身份运行"
echo 2. 安装完成后运行 verify_installation.bat 验证
echo 3. 运行 setup_intellij_idea.bat 配置 IntelliJ IDEA
echo.
pause