@echo off
echo 正在创建安装脚本...

REM 创建主安装脚本
echo @echo off > setup_dev_environment.bat
echo chcp 65001 ^>nul >> setup_dev_environment.bat
echo setlocal enabledelayedexpansion >> setup_dev_environment.bat
echo. >> setup_dev_environment.bat
echo echo ======================================== >> setup_dev_environment.bat
echo echo    PDF阅读器项目开发环境自动安装 >> setup_dev_environment.bat
echo echo ======================================== >> setup_dev_environment.bat

REM 这里您需要手动添加完整的脚本内容

echo 脚本文件已创建！
echo 请编辑 setup_dev_environment.bat 添加完整内容
pause